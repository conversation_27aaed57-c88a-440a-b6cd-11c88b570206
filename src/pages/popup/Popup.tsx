import React, { useEffect, useState } from 'react';
import '@pages/popup/Popup.css';
import withSuspense from '@src/shared/hoc/withSuspense';
import withErrorBoundary from '@src/shared/hoc/withErrorBoundary';

interface PageInfo {
    url: string;
    title: string;
    isTargetPage: boolean;
    tableCount: number;
    siteName?: string; // 用于将来支持多个站点
}

const Popup = () => {
    const [pageInfo, setPageInfo] = useState<PageInfo | null>(null);
    const [isDownloading, setIsDownloading] = useState(false);
    const [downloadType, setDownloadType] = useState<'csv' | 'excel' | null>(null);
    const [status, setStatus] = useState<'loading' | 'ready' | 'error'>('loading');
    const [errorMessage, setErrorMessage] = useState<string>('');


    useEffect(() => {
        console.log('🚀 [Popup] React组件已挂载，开始初始化...');
        initializePopup();
    }, []);

    const initializePopup = async () => {
        console.log('🎬 [Popup] 开始初始化...');
        setStatus('loading');

        try {
            console.log('🔍 [Popup] 获取当前标签页...');
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            console.log('📋 [Popup] 获取到标签页:', tab);

            if (!tab || !tab.url) {
                setStatus('error');
                setErrorMessage('无法获取当前页面信息');
                return;
            }

            // 检查是否为目标页面 - 支持多个站点扩展
            const supportedSites = [
                {
                    name: 'SQLWeb数据平台',
                    pattern: /https:\/\/shuidi\.unicom\.local\/sqlweb\/\?#\//,
                    id: 'sqlweb'
                }
                // 将来可以在这里添加更多支持的页面
                // {
                //     name: '其他数据平台',
                //     pattern: /https:\/\/other\.example\.com\/data/,
                //     id: 'other'
                // }
            ];

            const matchedSite = supportedSites.find(site => site.pattern.test(tab.url));
            const isTargetPage = !!matchedSite;
            console.log('🎯 [Popup] 目标页面检查:', {
                url: tab.url,
                isTargetPage,
                matchedSite: matchedSite?.name || '不支持'
            });

            if (isTargetPage) {
                // 只测试连接，不获取数据
                try {
                    console.log('🔄 测试content script连接...');
                    const pingResponse = await chrome.tabs.sendMessage(tab.id!, { action: 'ping' });
                    console.log('🏓 Ping测试结果:', pingResponse);

                    if (!pingResponse || !pingResponse.success) {
                        throw new Error('Content script未正确响应ping测试');
                    }

                    console.log('✅ Content script连接正常');

                    // 检测表格数量
                    console.log('🔍 开始检测表格...');
                    const tableResponse = await chrome.tabs.sendMessage(tab.id!, { action: 'getPageInfo' });
                    const tableCount = tableResponse?.success ? tableResponse.tableCount : 0;
                    console.log('📊 检测到表格数量:', tableCount);

                    // 设置页面信息，包含实际的表格数量
                    setPageInfo({
                        url: tab.url,
                        title: tab.title || '未知页面',
                        isTargetPage,
                        tableCount: tableCount,
                        siteName: matchedSite?.name || '未知站点'
                    });



                    setStatus('ready');
                } catch (error) {
                    console.error('💥 Content script通信失败:', error);

                    let errorMsg = '无法连接到页面';
                    if (error.message.includes('Could not establish connection')) {
                        errorMsg = 'Content script未加载，请刷新页面';
                    } else {
                        errorMsg = `连接错误: ${error.message}`;
                    }

                    setStatus('error');
                    setErrorMessage(errorMsg);
                }
            } else {
                // 非目标页面，直接显示信息
                setPageInfo({
                    url: tab.url,
                    title: tab.title || '未知页面',
                    isTargetPage: false,
                    tableCount: 0,
                    siteName: '不支持的页面'
                });
                setStatus('ready');
            }
        } catch (error) {
            console.error('初始化popup失败:', error);
            setStatus('error');
            setErrorMessage('初始化失败，请刷新页面后重试');
        }
    };



    const handleDownload = async (type: 'csv' | 'excel') => {
        if (!pageInfo) return;

        setIsDownloading(true);
        setDownloadType(type);

        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.id) {
                throw new Error('无法获取当前标签页');
            }

            // 发送下载请求到content script
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'downloadData',
                type: type
            });

            if (response.success) {
                showMessage(`${type.toUpperCase()} 文件下载成功！`, 'success');
            } else {
                throw new Error(response.error || '下载失败');
            }
        } catch (error) {
            console.error('下载失败:', error);
            showMessage(`下载失败: ${error}`, 'error');
        } finally {
            setIsDownloading(false);
            setDownloadType(null);
        }
    };

    const showMessage = (text: string, type: 'success' | 'error') => {
        // 简单的消息显示，可以后续优化
        if (type === 'success') {
            console.log('✅', text);
        } else {
            console.error('❌', text);
        }
    };



    const handleRefresh = () => {
        initializePopup();
    };

    const handleDetectTables = async () => {
        if (!pageInfo?.isTargetPage) return;

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!tab.id) return;

            console.log('🔍 重新检测表格...');
            const tableResponse = await chrome.tabs.sendMessage(tab.id, { action: 'getPageInfo' });
            const tableCount = tableResponse?.success ? tableResponse.tableCount : 0;
            console.log('📊 重新检测到表格数量:', tableCount);

            // 更新表格数量
            setPageInfo(prev => prev ? { ...prev, tableCount } : null);
            showMessage(`检测到 ${tableCount} 个表格`, 'success');
        } catch (error) {
            console.error('检测表格失败:', error);
            showMessage('检测表格失败', 'error');
        }
    };

    if (status === 'loading') {
        return (
            <div className="app">
                <div className="loading-screen">
                    <div className="loading-content">
                        <div className="loading-animation">
                            <div className="loading-circle"></div>
                            <div className="loading-circle"></div>
                            <div className="loading-circle"></div>
                        </div>
                        <div className="loading-text">
                            <h3>正在检测页面</h3>
                            <p>请稍候...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (status === 'error') {
        return (
            <div className="app">
                <div className="error-screen">
                    <div className="error-content">
                        <div className="error-illustration">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="15" y1="9" x2="9" y2="15"/>
                                <line x1="9" y1="9" x2="15" y2="15"/>
                            </svg>
                        </div>
                        <div className="error-text">
                            <h3>连接失败</h3>
                            <p>{errorMessage}</p>
                        </div>
                        <button className="retry-button" onClick={handleRefresh}>
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <polyline points="23,4 23,10 17,10"/>
                                <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"/>
                            </svg>
                            重新尝试
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="app">
            {/* 顶部状态栏 */}
            <div className="status-bar">
                <div className="connection-status">
                    {pageInfo?.isTargetPage ? (
                        <div className="status-indicator online">
                            <div className="pulse"></div>
                            <span>已连接</span>
                        </div>
                    ) : (
                        <div className="status-indicator offline">
                            <div className="pulse"></div>
                            <span>未连接</span>
                        </div>
                    )}
                </div>
                <div className="app-badge">
                    <span>数据导出</span>
                    <div className="version-tag">v1.0</div>
                </div>
            </div>

            {/* 主要内容卡片 */}
            <div className="main-card">
                {/* 页面信息面板 */}
                <div className="info-panel">
                    <div className="panel-header">
                        <div className="icon-wrapper">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <div className="panel-title">页面信息</div>
                    </div>

                    <div className="info-grid">
                        <div className="info-card">
                            <div className="info-label">当前页面</div>
                            <div className="info-value" title={pageInfo?.url}>
                                {pageInfo?.title || '未知页面'}
                            </div>
                        </div>

                        {pageInfo?.isTargetPage && (
                            <div className="info-card">
                                <div className="info-label">数据表格</div>
                                <div className="info-value highlight">
                                    {pageInfo.tableCount} 个
                                </div>
                            </div>
                        )}

                        <div className="info-card">
                            <div className="info-label">页面状态</div>
                            <div className={`info-value ${pageInfo?.isTargetPage ? 'success' : 'warning'}`}>
                                {pageInfo?.isTargetPage ? '支持导出' : '不支持'}
                            </div>
                        </div>
                    </div>
                </div>

                {/* 操作面板 */}
                {pageInfo?.isTargetPage ? (
                    <div className="action-panel">
                        <div className="panel-header">
                            <div className="icon-wrapper">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                            </div>
                            <div className="panel-title">数据导出</div>
                        </div>

                        <div className="export-grid">
                            <div
                                className={`export-option csv ${isDownloading && downloadType === 'csv' ? 'loading' : ''}`}
                                onClick={() => !isDownloading && handleDownload('csv')}
                            >
                                <div className="option-icon">
                                    {isDownloading && downloadType === 'csv' ? (
                                        <div className="loading-spinner"></div>
                                    ) : (
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                            <polyline points="14,2 14,8 20,8"/>
                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                        </svg>
                                    )}
                                </div>
                                <div className="option-content">
                                    <div className="option-title">CSV</div>
                                    <div className="option-desc">数据分析</div>
                                </div>
                            </div>

                            <div
                                className={`export-option excel ${isDownloading && downloadType === 'excel' ? 'loading' : ''}`}
                                onClick={() => !isDownloading && handleDownload('excel')}
                            >
                                <div className="option-icon">
                                    {isDownloading && downloadType === 'excel' ? (
                                        <div className="loading-spinner"></div>
                                    ) : (
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                            <polyline points="14,2 14,8 20,8"/>
                                            <line x1="12" y1="18" x2="12" y2="12"/>
                                            <line x1="9" y1="15" x2="15" y2="15"/>
                                        </svg>
                                    )}
                                </div>
                                <div className="option-content">
                                    <div className="option-title">Excel</div>
                                    <div className="option-desc">报表制作</div>
                                </div>
                            </div>

                            <div
                                className="export-option detect"
                                onClick={() => !isDownloading && handleDetectTables()}
                            >
                                <div className="option-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                </div>
                                <div className="option-content">
                                    <div className="option-title">检测</div>
                                    <div className="option-desc">重新扫描</div>
                                </div>
                            </div>

                            <div className="export-option info">
                                <div className="option-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <line x1="12" y1="16" x2="12" y2="12"/>
                                        <line x1="12" y1="8" x2="12.01" y2="8"/>
                                    </svg>
                                </div>
                                <div className="option-content">
                                    <div className="option-title">信息</div>
                                    <div className="option-desc">页面详情</div>
                                </div>
                            </div>
                        </div>

                    </div>
                ) : (
                    <div className="empty-state-panel">
                        <div className="empty-left">
                            <div className="empty-illustration">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                            </div>
                            <div className="empty-text">
                                <h3>页面未就绪</h3>
                                <p>当前页面不支持数据导出功能</p>
                            </div>
                        </div>

                        <div className="empty-right">
                            <div className="support-card">
                                <div className="support-header">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                                    </svg>
                                    <span>支持的平台</span>
                                </div>
                                <div className="support-list">
                                    <div className="support-item">
                                        <div className="support-icon">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                                                <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                                                <line x1="12" y1="22.08" x2="12" y2="12"/>
                                            </svg>
                                        </div>
                                        <div className="support-info">
                                            <div className="support-name">SQL Web 数据平台</div>
                                            <div className="support-url">shuidi.unicom.local</div>
                                        </div>
                                        <div className="support-status offline">离线</div>
                                    </div>
                                </div>
                                <div className="support-tip">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4"/>
                                        <path d="M12 8h.01"/>
                                    </svg>
                                    <span>请访问支持的平台后重新打开此工具</span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default withErrorBoundary(withSuspense(Popup, <div className="app"><div className="loading-screen"><div className="loading-content"><div className="loading-animation"><div className="loading-circle"></div></div><div className="loading-text"><h3>加载中...</h3></div></div></div></div>), <div className="app"><div className="error-screen"><div className="error-content"><h3>加载失败</h3><p>请刷新后重试</p></div></div></div>);
