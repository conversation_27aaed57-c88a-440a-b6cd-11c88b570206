.App {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    height: 100%;
    padding: 10px;
    background-color: #282c34;
}

.App-logo {
    height: 30vmin;
    pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
    .App-logo {
        animation: App-logo-spin infinite 20s linear;
    }
}

.App-header {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: calc(10px + 2vmin);
    color: white;
}

.App-link {
    color: #61dafb;
}

@keyframes App-logo-spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* 宽屏现代化数据导出工具 */

.app {
    width: 800px;
    height: 280px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    color: #f1f5f9;
    line-height: 1.4;
    overflow: hidden;
    box-sizing: border-box;
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    border: 1px solid #334155;
    position: relative;
    display: flex;
    flex-direction: column;
}

.app::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #3b82f6);
    border-radius: 16px 16px 0 0;
}

/* 加载屏幕 */
.loading-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 280px;
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.loading-content {
    text-align: center;
}

.loading-animation {
    display: flex;
    gap: 6px;
    justify-content: center;
    margin-bottom: 16px;
}

.loading-circle {
    width: 8px;
    height: 8px;
    background: #3b82f6;
    border-radius: 50%;
    animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-circle:nth-child(1) { animation-delay: -0.32s; }
.loading-circle:nth-child(2) { animation-delay: -0.16s; }

.loading-text h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #f1f5f9;
}

.loading-text p {
    margin: 0;
    color: #64748b;
    font-size: 12px;
}

/* 错误屏幕 */
.error-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 280px;
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.error-content {
    text-align: center;
    max-width: 300px;
}

.error-illustration {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    color: #ef4444;
}

.error-text h3 {
    margin: 0 0 6px 0;
    font-size: 16px;
    font-weight: 600;
    color: #f1f5f9;
}

.error-text p {
    margin: 0 0 20px 0;
    color: #64748b;
    font-size: 13px;
    line-height: 1.4;
}

.retry-button {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 auto;
}

.retry-button:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.retry-button svg {
    width: 14px;
    height: 14px;
}

/* 状态栏 */
.status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    background: rgba(30, 41, 59, 0.8);
    border-bottom: 1px solid #334155;
    backdrop-filter: blur(10px);
    flex-shrink: 0;
}

.connection-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator.online {
    color: #10b981;
}

.status-indicator.offline {
    color: #f59e0b;
}

.pulse {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse-glow 2s infinite;
}

.app-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    font-weight: 600;
    color: #cbd5e1;
}

.version-tag {
    background: #3b82f6;
    color: white;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 9px;
    font-weight: 700;
}

/* 主卡片 */
.main-card {
    padding: 12px 20px;
    background: transparent;
    flex: 1;
    display: flex;
    gap: 24px;
    overflow: hidden;
}

/* 面板通用样式 */
.info-panel {
    background: rgba(30, 41, 59, 0.6);
    border: 1px solid #334155;
    border-radius: 10px;
    padding: 14px;
    flex: 1;
    backdrop-filter: blur(10px);
    min-width: 200px;
}

.action-panel {
    background: rgba(30, 41, 59, 0.6);
    border: 1px solid #334155;
    border-radius: 10px;
    padding: 14px;
    flex: 3;
    backdrop-filter: blur(10px);
}

.panel-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.icon-wrapper {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.icon-wrapper svg {
    width: 14px;
    height: 14px;
}

.panel-title {
    font-size: 13px;
    font-weight: 700;
    color: #f1f5f9;
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
}

.info-card {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid #334155;
    border-radius: 8px;
    padding: 10px;
}

.info-label {
    font-size: 10px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.info-value {
    font-size: 12px;
    font-weight: 600;
    color: #f1f5f9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.info-value.highlight {
    color: #3b82f6;
}

.info-value.success {
    color: #10b981;
}

.info-value.warning {
    color: #f59e0b;
}

/* 导出网格 */
.export-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 16px;
    margin-bottom: 12px;
}

.export-option {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid #334155;
    border-radius: 10px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.export-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(59, 130, 246, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.export-option:hover::before {
    opacity: 1;
}

.export-option:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.2);
}

.export-option.loading {
    border-color: #3b82f6;
    pointer-events: none;
}

.option-icon {
    width: 32px;
    height: 32px;
    margin: 0 auto 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.export-option.csv .option-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.export-option.excel .option-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.export-option.detect .option-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.export-option.info .option-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.option-icon svg {
    width: 16px;
    height: 16px;
}

.option-content {
    position: relative;
    z-index: 1;
}

.option-title {
    font-size: 13px;
    font-weight: 700;
    color: #f1f5f9;
    margin-bottom: 2px;
}

.option-desc {
    font-size: 10px;
    color: #64748b;
    font-weight: 500;
}

/* 工具操作 */
.utility-actions {
    border-top: 1px solid #334155;
    padding-top: 8px;
}

.utility-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    width: 100%;
    padding: 8px;
    background: transparent;
    border: 1px dashed #475569;
    border-radius: 8px;
    color: #64748b;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.utility-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.utility-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.utility-btn svg {
    width: 12px;
    height: 12px;
}

/* 空状态面板 */
.empty-panel {
    background: rgba(30, 41, 59, 0.6);
    border: 1px solid #334155;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    flex: 2;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-content {
    max-width: 300px;
}

.empty-illustration {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    color: #475569;
}

.empty-text h3 {
    margin: 0 0 6px 0;
    font-size: 14px;
    font-weight: 600;
    color: #f1f5f9;
}

.empty-text p {
    margin: 0 0 16px 0;
    color: #64748b;
    font-size: 12px;
    line-height: 1.4;
}

.supported-info {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid #334155;
    border-radius: 8px;
    padding: 12px;
}

.support-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.support-item:last-child {
    margin-bottom: 0;
}

.support-label {
    font-size: 10px;
    color: #64748b;
    font-weight: 600;
}

.support-value {
    font-size: 10px;
    color: #f1f5f9;
    font-weight: 600;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 加载动画 */
.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 动画定义 */
@keyframes loading-bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 820px) {
    .app {
        width: 100%;
        height: auto;
        min-height: 280px;
        border-radius: 12px;
    }

    .main-card {
        flex-direction: column;
        gap: 12px;
    }

    .info-panel, .action-panel {
        flex: none;
    }

    .export-grid {
        grid-template-columns: 1fr 1fr;
    }
}

/* 减少动画支持 */
@media (prefers-reduced-motion: reduce) {
    .app *,
    .app *::before,
    .app *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 加载动画 */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 响应式调整 */
@media (max-width: 450px) {
    .popup {
        width: 100%;
        border-radius: 12px;
    }

    .header {
        padding: 16px 20px;
    }

    .actions {
        padding: 20px;
    }

    .header-top {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .info-value {
        max-width: 150px;
    }
}

/* 减少动画支持 */
@media (prefers-reduced-motion: reduce) {
    .popup *,
    .popup *::before,
    .popup *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}